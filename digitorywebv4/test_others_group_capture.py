#!/usr/bin/env python3
"""
Test script to verify that the OTHERS group captures all unmapped data correctly.
This ensures no data is lost in reconciliation calculations.
"""

import pandas as pd
import sys
import os

# Add the path to import dashboard_agents
sys.path.append('/home/<USER>/other/digii/digitoryjobsv4/app/utility')

try:
    from dashboard_agents import create_reconciliation_table_data, generate_group_level_reconciliation_data
    print("✅ Successfully imported dashboard_agents")
except ImportError as e:
    print(f"❌ Failed to import dashboard_agents: {e}")
    sys.exit(1)

def test_others_group_capture():
    """Test that unmapped categories and workareas are captured in OTHERS group"""
    
    print("\n🧪 Testing OTHERS Group Data Capture")
    print("=" * 50)
    
    # Create test data with both mapped and unmapped categories
    store_data = {
        'Category': ['FOOD', 'BEVERAGES', 'CLEANING_SUPPLIES', 'OFFICE_SUPPLIES', 'UNKNOWN_CATEGORY'],
        'Sub Category': ['Rice', 'Soft Drinks', 'Detergent', 'Paper', 'Mystery Item'],
        'Opening Amount': [1000, 500, 200, 100, 300],
        'Closing Amount': [800, 400, 150, 80, 250],
        'Purchase Amount': [2000, 1000, 300, 150, 400],
        'Ibt In Amount': [100, 50, 20, 10, 30],
        'Ibt Out Amount': [50, 25, 10, 5, 15],
        'Return Qty Amount': [20, 10, 5, 2, 8],
        'Spoilage Amount': [30, 15, 8, 3, 12],
        'Indent Amount': [0, 0, 0, 0, 0]
    }
    
    consumption_data = {
        'Category': ['FOOD', 'BEVERAGES', 'CLEANING_SUPPLIES', 'OFFICE_SUPPLIES', 'UNKNOWN_CATEGORY', 'ANOTHER_UNMAPPED'],
        'Sub Category': ['Rice', 'Soft Drinks', 'Detergent', 'Paper', 'Mystery Item', 'Random Item'],
        'WorkArea': ['KITCHEN', 'BAR', 'HOUSEKEEPING', 'ADMIN', 'UNKNOWN_AREA', 'RANDOM_AREA'],
        'WAC(incl.tax,etc)': [10, 15, 8, 5, 12, 20],
        'WorkArea Opening(incl.tax,etc)': [500, 300, 100, 50, 150, 200],
        'WorkArea Closing(incl.tax,etc)': [400, 250, 80, 40, 120, 160],
        'WorkArea Transfer In': [50, 30, 10, 5, 20, 25],
        'WorkArea Transfer Out': [20, 15, 5, 2, 8, 10],
        'Return To Store Out': [10, 5, 2, 1, 4, 5],
        'Spoilage/Adjustments': [5, 3, 1, 0, 2, 3]
    }
    
    store_df = pd.DataFrame(store_data)
    consumption_df = pd.DataFrame(consumption_data)
    
    # Create mappings for only some categories (FOOD and BEVERAGES)
    category_workarea_mappings = [
        {'categoryName': 'FOOD', 'workAreas': ['KITCHEN']},
        {'categoryName': 'BEVERAGES', 'workAreas': ['BAR']}
    ]
    
    print("📊 Test Data Summary:")
    print(f"   Store categories: {list(store_df['Category'].unique())}")
    print(f"   Consumption categories: {list(consumption_df['Category'].unique())}")
    print(f"   Mapped categories: {[m['categoryName'] for m in category_workarea_mappings]}")
    print(f"   Expected unmapped: CLEANING_SUPPLIES, OFFICE_SUPPLIES, UNKNOWN_CATEGORY, ANOTHER_UNMAPPED")
    
    # Test reconciliation data creation
    print("\n🔍 Testing Reconciliation Data Creation...")
    reconciliation_data = create_reconciliation_table_data(store_df, consumption_df, category_workarea_mappings)
    
    print(f"✅ Categories in reconciliation data: {list(reconciliation_data.keys())}")
    
    # Verify OTHERS category exists and contains unmapped data
    if 'OTHERS' in reconciliation_data:
        others_data = reconciliation_data['OTHERS']
        others_subcategories = list(others_data['subcategories'].keys())
        others_consumption = others_data['totals']['consumption']
        
        print(f"✅ OTHERS category found with subcategories: {others_subcategories}")
        print(f"✅ OTHERS total consumption: ₹{others_consumption:,.2f}")
        
        # Check if unmapped subcategories are captured
        expected_unmapped_subcats = ['Detergent', 'Paper', 'Mystery Item', 'Random Item']
        captured_unmapped = [subcat for subcat in expected_unmapped_subcats if subcat in others_subcategories]
        
        print(f"✅ Captured unmapped subcategories: {captured_unmapped}")
        
        if len(captured_unmapped) > 0:
            print("🎉 SUCCESS: Unmapped data successfully captured in OTHERS group!")
        else:
            print("❌ ISSUE: No unmapped subcategories found in OTHERS group")
    else:
        print("❌ ISSUE: OTHERS category not found in reconciliation data")
    
    # Test group-level data generation
    print("\n🔍 Testing Group-Level Data Generation...")
    
    department_group_category_mappings = [
        {'groupId': 'FOOD_DEPT', 'groupName': 'FOOD_DEPARTMENT', 'categories': ['FOOD']},
        {'groupId': 'BEV_DEPT', 'groupName': 'BEVERAGE_DEPARTMENT', 'categories': ['BEVERAGES']}
    ]
    
    department_group_workarea_mappings = [
        {'groupId': 'FOOD_DEPT', 'groupName': 'FOOD_DEPARTMENT', 'workAreas': ['KITCHEN']},
        {'groupId': 'BEV_DEPT', 'groupName': 'BEVERAGE_DEPARTMENT', 'workAreas': ['BAR']}
    ]
    
    group_data = generate_group_level_reconciliation_data(
        store_df, consumption_df, 
        department_group_category_mappings, 
        department_group_workarea_mappings
    )
    
    print(f"✅ Groups in group data: {list(group_data.keys())}")
    
    if 'OTHERS' in group_data:
        others_group = group_data['OTHERS']
        others_group_consumption = others_group['totals']['consumption']
        
        print(f"✅ OTHERS group found with consumption: ₹{others_group_consumption:,.2f}")
        print("🎉 SUCCESS: OTHERS group successfully created at department level!")
    else:
        print("❌ ISSUE: OTHERS group not found in group data")
    
    # Calculate total data coverage
    print("\n📈 Data Coverage Analysis:")
    
    total_store_value = store_df['Purchase Amount'].sum()
    total_consumption_value = consumption_df['WAC(incl.tax,etc)'].sum() * 100  # Rough estimate
    
    mapped_consumption = sum(data['totals']['consumption'] for name, data in reconciliation_data.items() if name != 'OTHERS')
    others_consumption = reconciliation_data.get('OTHERS', {}).get('totals', {}).get('consumption', 0)
    total_captured_consumption = mapped_consumption + others_consumption
    
    print(f"   Total store purchase value: ₹{total_store_value:,.2f}")
    print(f"   Mapped categories consumption: ₹{mapped_consumption:,.2f}")
    print(f"   OTHERS category consumption: ₹{others_consumption:,.2f}")
    print(f"   Total captured consumption: ₹{total_captured_consumption:,.2f}")
    
    if others_consumption > 0:
        others_percentage = (others_consumption / total_captured_consumption * 100) if total_captured_consumption > 0 else 0
        print(f"   OTHERS represents {others_percentage:.1f}% of total consumption")
        print("🎉 SUCCESS: No data lost - all unmapped data captured in OTHERS!")
    else:
        print("⚠️  WARNING: OTHERS consumption is 0 - verify unmapped data handling")
    
    return reconciliation_data, group_data

def main():
    """Main test function"""
    print("🔍 OTHERS Group Data Capture Test")
    print("=" * 60)
    print("This test verifies that all unmapped categories, subcategories,")
    print("and workareas are properly captured in the OTHERS group.")
    print("=" * 60)
    
    try:
        reconciliation_data, group_data = test_others_group_capture()
        
        print("\n" + "=" * 60)
        print("🎉 TEST COMPLETED SUCCESSFULLY!")
        print("✅ All unmapped data is now captured in OTHERS group")
        print("✅ No inventory data will be lost in reconciliation")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
